/**
 * FAISS Service - Handles all FAISS-related API calls
 * Replaces Pinecone-specific functionality with FAISS backend integration
 */

export interface EmbeddingModel {
  name: string;
  dimension: number;
  description: string;
}

export interface FaissCategory {
  index_name: string;
  email?: string;
  embedding_model?: string;
  embedding_dimension?: number;
  created_at?: string;
}

export interface FaissQueryResult {
  rank: number;
  score: string;
  metadata: any;
  text: string;
}

export interface UploadProgress {
  upload_id: string;
  status: 'processing' | 'complete' | 'cancelled' | 'error';
  total_rows: number;
  processed_rows: number;
  total_vectors: number;
  index_name: string;
  cancelled: boolean;
  processing_time?: number;
}

// Base URL for FAISS backend
const FAISS_BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:5010' 
  : 'http://localhost:5010';

/**
 * Upload CSV file to FAISS backend
 */
export const uploadCSVToFaiss = async (
  file: File,
  indexName: string,
  clientEmail?: string,
  updateMode: 'update' | 'new' = 'update',
  embedModel: string = 'all-MiniLM-L6-v2',
  signal?: AbortSignal,
  onProgress?: (progress: number) => void
): Promise<any> => {
  return new Promise((resolve, reject) => {
    try {
      // Validate file type
      if (file.type !== 'text/csv' && !file.name.toLowerCase().endsWith('.csv')) {
        reject(new Error('Only CSV files are supported for CSV upload'));
        return;
      }

      // Create FormData
      const formData = new FormData();
      formData.append('file', file);
      formData.append('index_name', indexName);
      formData.append('update_mode', updateMode);
      formData.append('embed_model', embedModel);
      
      if (clientEmail) {
        formData.append('client', clientEmail);
      }

      // Create XMLHttpRequest for progress tracking
      const xhr = new XMLHttpRequest();

      // Handle response
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (e) {
            reject(new Error('Invalid JSON response from server'));
          }
        } else {
          try {
            const errorResponse = JSON.parse(xhr.responseText);
            reject(new Error(errorResponse.error || `HTTP ${xhr.status}: ${xhr.statusText}`));
          } catch (e) {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
          }
        }
      };

      xhr.onerror = () => {
        reject(new Error('Network error occurred during upload'));
      };

      xhr.onabort = () => {
        reject(new Error('Upload was cancelled'));
      };

      // Track upload progress
      if (onProgress) {
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            onProgress(progress);
          }
        };
      }

      // Handle cancellation
      if (signal) {
        signal.addEventListener('abort', () => {
          xhr.abort();
        });
      }

      // Send request
      xhr.open('POST', `${FAISS_BASE_URL}/api/upload-csv`, true);
      xhr.send(formData);

    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Upload Excel file to FAISS backend
 */
export const uploadExcelToFaiss = async (
  file: File,
  indexName: string,
  clientId: string,
  updateMode: 'update' | 'new' = 'update',
  embedModel: string = 'all-MiniLM-L6-v2',
  signal?: AbortSignal,
  onProgress?: (progress: number) => void
): Promise<any> => {
  return new Promise((resolve, reject) => {
    try {
      // Validate file type
      const fileName = file.name.toLowerCase();
      if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
        reject(new Error('Only Excel files (.xlsx, .xls) are supported for Excel upload'));
        return;
      }

      // Create FormData
      const formData = new FormData();
      formData.append('file', file);
      formData.append('index_name', indexName);
      formData.append('client_id', clientId);
      formData.append('update_mode', updateMode);
      formData.append('embed_model', embedModel);

      // Create XMLHttpRequest for progress tracking
      const xhr = new XMLHttpRequest();

      // Handle response
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (e) {
            reject(new Error('Invalid JSON response from server'));
          }
        } else {
          try {
            const errorResponse = JSON.parse(xhr.responseText);
            reject(new Error(errorResponse.error?.message || errorResponse.message || `HTTP ${xhr.status}: ${xhr.statusText}`));
          } catch (e) {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
          }
        }
      };

      xhr.onerror = () => {
        reject(new Error('Network error occurred during upload'));
      };

      xhr.onabort = () => {
        reject(new Error('Upload was cancelled'));
      };

      // Track upload progress
      if (onProgress) {
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            onProgress(progress);
          }
        };
      }

      // Handle cancellation
      if (signal) {
        signal.addEventListener('abort', () => {
          xhr.abort();
        });
      }

      // Send request
      xhr.open('POST', `${FAISS_BASE_URL}/api/upload-excel`, true);
      xhr.send(formData);

    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Upload file to FAISS backend (supports both CSV and Excel)
 */
export const uploadFileToFaiss = async (
  file: File,
  indexName: string,
  clientEmail?: string,
  updateMode: 'update' | 'new' = 'update',
  embedModel: string = 'all-MiniLM-L6-v2',
  signal?: AbortSignal,
  onProgress?: (progress: number) => void
): Promise<any> => {
  const fileName = file.name.toLowerCase();
  const isExcel = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');

  if (isExcel) {
    // For Excel files, client_id is required
    if (!clientEmail) {
      throw new Error('Client email is required for Excel file uploads');
    }
    return uploadExcelToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);
  } else {
    // For CSV files, use the existing CSV upload function
    return uploadCSVToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);
  }
};

/**
 * Get list of available embedding models
 */
export const getEmbeddingModels = async (): Promise<{ models: Record<string, EmbeddingModel>, default_model: string }> => {
  const response = await fetch(`${FAISS_BASE_URL}/api/list-embedding-models`);
  if (!response.ok) {
    throw new Error(`Failed to fetch embedding models: ${response.statusText}`);
  }
  return response.json();
};

/**
 * Get list of FAISS categories/indexes
 */
export const getFaissCategories = async (clientEmail?: string): Promise<FaissCategory[]> => {
  const response = await fetch(`${FAISS_BASE_URL}/api/list-categories`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(clientEmail ? { client_email: clientEmail } : {})
  });
  
  if (!response.ok) {
    throw new Error(`Failed to fetch FAISS categories: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data.categories || [];
};

/**
 * Check if FAISS index exists
 */
export const checkFaissIndexExists = async (indexName: string, embedModel?: string): Promise<boolean> => {
  const response = await fetch(`${FAISS_BASE_URL}/api/check-index`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      index_name: indexName,
      embed_model: embedModel
    })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to check FAISS index: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data.exists || false;
};

/**
 * Query FAISS index
 */
export const queryFaiss = async (
  query: string, 
  indexName: string, 
  k: number = 5
): Promise<FaissQueryResult[]> => {
  const response = await fetch(`${FAISS_BASE_URL}/api/query-faiss`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query,
      index_name: indexName,
      k
    })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to query FAISS index: ${response.statusText}`);
  }
  
  const data = await response.json();
  return data.results || [];
};

/**
 * Get upload status
 */
export const getUploadStatus = async (uploadId: string): Promise<UploadProgress> => {
  const response = await fetch(`${FAISS_BASE_URL}/api/upload-status`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ upload_id: uploadId })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to get upload status: ${response.statusText}`);
  }
  
  return response.json();
};

/**
 * Cancel upload
 */
export const cancelUpload = async (uploadId: string): Promise<void> => {
  const response = await fetch(`${FAISS_BASE_URL}/api/cancel-upload`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ upload_id: uploadId })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to cancel upload: ${response.statusText}`);
  }
};

/**
 * Get CSV data from database
 */
export const getCSVData = async (
  indexName: string, 
  limit: number = 100, 
  offset: number = 0
): Promise<any> => {
  const response = await fetch(`${FAISS_BASE_URL}/api/get-csv-data`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      index_name: indexName,
      limit,
      offset
    })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to get CSV data: ${response.statusText}`);
  }
  
  return response.json();
};

/**
 * List CSV files in database
 */
export const listCSVFiles = async (clientEmail?: string): Promise<any[]> => {
  const response = await fetch(`${FAISS_BASE_URL}/api/list-csv-files`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(clientEmail ? { client_email: clientEmail } : {})
  });

  if (!response.ok) {
    throw new Error(`Failed to list CSV files: ${response.statusText}`);
  }

  const data = await response.json();
  return data.files || [];
};

/**
 * List Excel files in database
 */
export const listExcelFiles = async (clientId?: string): Promise<any[]> => {
  const response = await fetch(`${FAISS_BASE_URL}/api/list-excel-files`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(clientId ? { client_id: clientId } : {})
  });

  if (!response.ok) {
    throw new Error(`Failed to list Excel files: ${response.statusText}`);
  }

  const data = await response.json();
  return data.excel_files || [];
};

// Utility functions for localStorage management
export const getFaissConfig = () => {
  if (typeof window === 'undefined') return null;
  
  return {
    indexName: localStorage.getItem('faiss_index_name'),
    embedModel: localStorage.getItem('faiss_embed_model') || 'all-MiniLM-L6-v2',
    clientEmail: localStorage.getItem('faiss_client_email')
  };
};

export const setFaissConfig = (config: {
  indexName?: string;
  embedModel?: string;
  clientEmail?: string;
}) => {
  if (typeof window === 'undefined') return;
  
  if (config.indexName) {
    localStorage.setItem('faiss_index_name', config.indexName);
  }
  if (config.embedModel) {
    localStorage.setItem('faiss_embed_model', config.embedModel);
  }
  if (config.clientEmail) {
    localStorage.setItem('faiss_client_email', config.clientEmail);
  }
};
