"""
Translation Service for FAISS Backend
Provides language detection and translation capabilities for bot responses.
"""

import os
import re
import json
import time
import hashlib
import requests
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

class TranslationService:
    """
    Service for handling language detection and translation of bot responses.
    Supports multiple translation providers with caching for performance.
    """
    
    def __init__(self):
        self.cache = {}  # In-memory cache for translations
        self.cache_expiry = 3600  # 1 hour cache expiry
        self.supported_languages = {
            'en': 'English',
            'ta': 'Tamil',
            'hi': 'Hindi',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'ar': 'Arabic'
        }
        
    def detect_language(self, text: str) -> str:
        """
        Detect the language of the input text.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Language code (e.g., 'en', 'ta', 'hi')
        """
        if not text or not text.strip():
            return 'en'  # Default to English
            
        # Tamil detection using Unicode ranges
        if self._is_tamil_text(text):
            return 'ta'
            
        # Hindi detection using Unicode ranges
        if self._is_hindi_text(text):
            return 'hi'
            
        # Arabic detection
        if self._is_arabic_text(text):
            return 'ar'
            
        # Chinese detection
        if self._is_chinese_text(text):
            return 'zh'
            
        # Default to English for other cases
        return 'en'
    
    def _is_tamil_text(self, text: str) -> bool:
        """Check if text contains Tamil characters"""
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        return bool(tamil_pattern.search(text))
    
    def _is_hindi_text(self, text: str) -> bool:
        """Check if text contains Hindi/Devanagari characters"""
        hindi_pattern = re.compile(r'[\u0900-\u097F]')
        return bool(hindi_pattern.search(text))
    
    def _is_arabic_text(self, text: str) -> bool:
        """Check if text contains Arabic characters"""
        arabic_pattern = re.compile(r'[\u0600-\u06FF]')
        return bool(arabic_pattern.search(text))
    
    def _is_chinese_text(self, text: str) -> bool:
        """Check if text contains Chinese characters"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))
    
    def _generate_cache_key(self, text: str, source_lang: str, target_lang: str) -> str:
        """Generate a cache key for translation"""
        content = f"{text}|{source_lang}|{target_lang}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """Check if cache entry is still valid"""
        if 'timestamp' not in cache_entry:
            return False
        
        cache_time = datetime.fromisoformat(cache_entry['timestamp'])
        expiry_time = cache_time + timedelta(seconds=self.cache_expiry)
        return datetime.now() < expiry_time
    
    def translate_text(self, text: str, target_lang: str, source_lang: str = None) -> Dict[str, str]:
        """
        Translate text to target language.
        
        Args:
            text: Text to translate
            target_lang: Target language code
            source_lang: Source language code (auto-detect if None)
            
        Returns:
            Dictionary with translation result and metadata
        """
        if not text or not text.strip():
            return {
                'translated_text': text,
                'source_language': 'unknown',
                'target_language': target_lang,
                'translation_provider': 'none',
                'cached': False
            }
        
        # Auto-detect source language if not provided
        if not source_lang:
            source_lang = self.detect_language(text)
        
        # If source and target are the same, return original text
        if source_lang == target_lang:
            return {
                'translated_text': text,
                'source_language': source_lang,
                'target_language': target_lang,
                'translation_provider': 'none',
                'cached': False
            }
        
        # Check cache first
        cache_key = self._generate_cache_key(text, source_lang, target_lang)
        if cache_key in self.cache and self._is_cache_valid(self.cache[cache_key]):
            cached_result = self.cache[cache_key]
            return {
                'translated_text': cached_result['translated_text'],
                'source_language': source_lang,
                'target_language': target_lang,
                'translation_provider': cached_result.get('provider', 'cached'),
                'cached': True
            }
        
        # Attempt translation using available providers
        translation_result = self._attempt_translation(text, source_lang, target_lang)
        
        # Cache the result
        if translation_result['success']:
            self.cache[cache_key] = {
                'translated_text': translation_result['translated_text'],
                'provider': translation_result['provider'],
                'timestamp': datetime.now().isoformat()
            }
        
        return {
            'translated_text': translation_result['translated_text'],
            'source_language': source_lang,
            'target_language': target_lang,
            'translation_provider': translation_result['provider'],
            'cached': False
        }
    
    def _attempt_translation(self, text: str, source_lang: str, target_lang: str) -> Dict:
        """
        Attempt translation using available providers.
        
        Returns:
            Dictionary with translation result
        """
        # For now, we'll use a fallback approach since external APIs have CORS issues
        # In production, you would implement actual translation service calls here
        
        # Fallback translations for common cases
        fallback_translations = self._get_fallback_translations(text, source_lang, target_lang)
        
        if fallback_translations:
            return {
                'success': True,
                'translated_text': fallback_translations,
                'provider': 'fallback'
            }
        
        # If no fallback available, return original text
        return {
            'success': False,
            'translated_text': text,
            'provider': 'none'
        }
    
    def _get_fallback_translations(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Provide fallback translations for common phrases and responses.
        This is a simplified approach - in production you'd use proper translation APIs.
        """
        
        # Common response patterns and their translations
        translation_patterns = {
            ('en', 'ta'): {
                'Based on the provided information': 'வழங்கப்பட்ட தகவலின் அடிப்படையில்',
                'According to the document': 'ஆவணத்தின் படி',
                'The key points are': 'முக்கிய புள்ளிகள்',
                'In summary': 'சுருக்கமாக',
                'Therefore': 'எனவே',
                'However': 'இருப்பினும்',
                'Additionally': 'கூடுதலாக',
                'Furthermore': 'மேலும்'
            },
            ('ta', 'en'): {
                'வழங்கப்பட்ட தகவலின் அடிப்படையில்': 'Based on the provided information',
                'ஆவணத்தின் படி': 'According to the document',
                'முக்கிய புள்ளிகள்': 'The key points are',
                'சுருக்கமாக': 'In summary',
                'எனவே': 'Therefore',
                'இருப்பினும்': 'However',
                'கூடுதலாக': 'Additionally',
                'மேலும்': 'Furthermore'
            }
        }
        
        pattern_key = (source_lang, target_lang)
        if pattern_key in translation_patterns:
            patterns = translation_patterns[pattern_key]
            
            # Simple pattern matching and replacement
            translated_text = text
            for source_phrase, target_phrase in patterns.items():
                translated_text = translated_text.replace(source_phrase, target_phrase)
            
            # If any replacements were made, return the translated text
            if translated_text != text:
                return translated_text
        
        return None
    
    def translate_response_data(self, response_data: Dict, target_lang: str) -> Dict:
        """
        Translate all text fields in a response data dictionary.
        
        Args:
            response_data: Response data containing text fields to translate
            target_lang: Target language code
            
        Returns:
            Translated response data
        """
        translated_data = response_data.copy()
        
        # Translate main AI response
        if 'ai_response' in translated_data and translated_data['ai_response']:
            translation_result = self.translate_text(
                translated_data['ai_response'], 
                target_lang
            )
            translated_data['ai_response'] = translation_result['translated_text']
            translated_data['translation_metadata'] = {
                'ai_response': translation_result
            }
        
        # Translate related questions
        if 'related_questions' in translated_data and translated_data['related_questions']:
            translated_questions = []
            question_translations = []
            
            for question in translated_data['related_questions']:
                translation_result = self.translate_text(question, target_lang)
                translated_questions.append(translation_result['translated_text'])
                question_translations.append(translation_result)
            
            translated_data['related_questions'] = translated_questions
            if 'translation_metadata' not in translated_data:
                translated_data['translation_metadata'] = {}
            translated_data['translation_metadata']['related_questions'] = question_translations
        
        # Add language metadata
        translated_data['response_language'] = target_lang
        translated_data['translation_timestamp'] = datetime.now().isoformat()
        
        return translated_data
    
    def get_cache_stats(self) -> Dict:
        """Get translation cache statistics"""
        valid_entries = 0
        expired_entries = 0
        
        for cache_entry in self.cache.values():
            if self._is_cache_valid(cache_entry):
                valid_entries += 1
            else:
                expired_entries += 1
        
        return {
            'total_entries': len(self.cache),
            'valid_entries': valid_entries,
            'expired_entries': expired_entries,
            'cache_expiry_seconds': self.cache_expiry,
            'supported_languages': self.supported_languages
        }
    
    def clear_cache(self):
        """Clear the translation cache"""
        self.cache.clear()

# Global translation service instance
translation_service = TranslationService()
