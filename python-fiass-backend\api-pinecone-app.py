from flask import Flask, request, jsonify
from flask_cors import CORS
import faiss
import json
import numpy as np
from langchain_huggingface.embeddings import HuggingFaceEmbeddings
from openai import OpenAI
from dotenv import load_dotenv
import os
import re

# Load environment variables
load_dotenv()

# Configuration
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
INDEX_PATH = os.path.join(os.getcwd(), "news_index.faiss")
METADATA_PATH = os.path.join(os.getcwd(), "news_metadata.json")

# Initialize services
app = Flask(__name__)
CORS(app, resources={
    r"/*": {
        "origins": [
            "http://localhost:3000",
            "http://localhost:3001",
            "http://127.0.0.1:3000",
            "http://127.0.0.1:3001",
            "http://**************:3000",
            "http://**************:3001"
        ],
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "X-Requested-With", "Origin"],
        "supports_credentials": True
    }
})

# Load FAISS index and metadata
try:
    faiss_index = faiss.read_index(INDEX_PATH)
    with open(METADATA_PATH, "r", encoding="utf-8") as f:
        metadata_store = json.load(f)
    print(f"✅ Loaded FAISS index with {faiss_index.ntotal} vectors")
except Exception as e:
    print(f"❌ Error loading FAISS index: {e}")
    faiss_index = None
    metadata_store = []

embedder = HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")
deepseek_client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url="https://api.deepseek.com")

def retrieve_from_faiss(query, k=5):
    """
    Search the FAISS index with a query and return top-k matches.
    """
    if faiss_index is None or not metadata_store:
        return []

    # Embed the query
    query_vector = embedder.embed_documents([query])[0]
    query_embedding = np.array([query_vector]).astype("float32")

    # Normalize for cosine similarity
    faiss.normalize_L2(query_embedding)

    # Search the index
    distances, indices = faiss_index.search(query_embedding, k)

    results = []
    for rank, idx in enumerate(indices[0]):
        if idx < len(metadata_store) and idx >= 0:
            meta = metadata_store[idx]
            # Create a match object similar to Pinecone's structure
            match = type('Match', (), {
                'score': float(distances[0][rank]),
                'metadata': {
                    'chunk_text': meta.get("chunk_text", ""),
                    'record_date': meta.get("record_date", "Unknown"),
                    'category': meta.get("category", "N/A"),
                    'url': meta.get("url", "N/A"),
                    'summary': meta.get("summary", "N/A")
                }
            })()
            results.append(match)

    return results

def generate_response(query, context_docs):
    context = "\n\n".join([doc.metadata.get('chunk_text', '') for doc in context_docs])
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": f"""Answer the following question based on this information:

CONTEXT:
{context}

QUESTION: {query}
"""}
    ]
    response = deepseek_client.chat.completions.create(
        model="deepseek-chat",
        messages=messages,
        stream=False
    )
    return response.choices[0].message.content

def generate_related_questions(query, answer, selected_language="English"):
    # Create language-specific prompts
    if selected_language == "Tamil":
        prompt = f"""
கொடுக்கப்பட்ட கேள்வி மற்றும் பதிலின் அடிப்படையில், 5 சூழல் சார்ந்த தொடர்ச்சி கேள்விகளை உருவாக்கவும். கேள்விகள் தமிழில் மட்டுமே இருக்க வேண்டும்.

கேள்வி: {query}
பதில்: {answer}

தொடர்புடைய கேள்விகள்:
1.
"""
    else:
        prompt = f"""
Based on the following question and answer, generate 5 contextually relevant follow-up questions.

QUESTION: {query}
ANSWER: {answer}

RELATED QUESTIONS:
1.
"""

    messages = [{"role": "user", "content": prompt}]
    try:
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages
        )
        raw_text = response.choices[0].message.content
        lines = raw_text.strip().split('\n')
        questions = [
            re.sub(r"^\d+\.\s*", "", line).strip()
            for line in lines
            if re.search(r'\?', line)
        ]
        return questions[:5]
    except Exception as e:
        if selected_language == "Tamil":
            return ["இந்த தகவலின் முக்கிய அம்சங்கள் என்ன?",
                    "இந்த தலைப்பு பற்றி மேலும் விவரங்கள் தர முடியுமா?",
                    "இந்த தகவலின் தாக்கங்கள் என்ன?",
                    "இது தற்போதைய போக்குகளுடன் எவ்வாறு தொடர்புடையது?",
                    "இதன் சாத்தியமான நன்மைகள் மற்றும் தீமைகள் என்ன?"]
        else:
            return [f"(Could not generate related questions: {str(e)})"]

def extract_sentences(text):
    return re.split(r'(?<=[.!?])\s+', text.strip())

def enrich_ai_response_with_urls(ai_response):
    sentences = extract_sentences(ai_response)
    enriched = []

    for sentence in sentences:
        if not sentence.strip():
            continue
        matches = retrieve_from_faiss(sentence, k=1)
        if matches:
            top_match = matches[0].metadata
            enriched.append({
                "sentence": sentence,
                "url": top_match.get("url", "N/A"),
                "summary": top_match.get("summary", "N/A")
            })
        else:
            enriched.append({
                "sentence": sentence,
                "url": "Not found",
                "summary": "No summary found"
            })

    return enriched

@app.route('/financial_query', methods=['POST'])
def handle_query():
    data = request.get_json()
    query = data.get("query", "").strip()

    # Get language preference for Tamil support
    selected_language = data.get("language", "English")

    # Auto-detect Tamil language from query text if not explicitly set
    def is_tamil_text(text):
        """Check if text contains Tamil characters"""
        import re
        # Tamil Unicode range: \u0B80-\u0BFF
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        return bool(tamil_pattern.search(text))

    # If language is not explicitly set to Tamil but query contains Tamil text, auto-detect
    if selected_language != "Tamil" and is_tamil_text(query):
        selected_language = "Tamil"
        print(f"🔍 AUTO-DETECTED TAMIL TEXT in query: '{query[:50]}...'")
        print(f"🌏 LANGUAGE AUTO-DETECTION: Switching to Tamil language processing")

    if not query:
        return jsonify({"error": "Query is required."}), 400

    matches = retrieve_from_faiss(query)
    retrieved_docs = []
    for i, match in enumerate(matches):
        metadata = match.metadata
        score = round(match.score * 100, 2)
        retrieved_docs.append({
            "rank": i + 1,
            "score": f"{score}%",
            "date": metadata.get('record_date', 'Unknown'),
            "category": metadata.get('category', 'N/A'),
            "text": metadata.get('chunk_text', 'No text')
        })

    ai_response = generate_response(query, matches)
    enriched_sentences = enrich_ai_response_with_urls(ai_response)
    related_questions = generate_related_questions(query, ai_response, selected_language)

    return jsonify({
        "query": query,
        "retrieved_documents": retrieved_docs,
        "ai_response": ai_response,
        "sentence_analysis": enriched_sentences,
        "related_questions": related_questions
    })

if __name__ == '__main__':
    app.run(debug=True)
