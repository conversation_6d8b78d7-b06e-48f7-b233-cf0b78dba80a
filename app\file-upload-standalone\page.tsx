'use client';

import React, { useState, useEffect } from 'react';
import FileUploadPage from '@/components/FileUploadPage';
import { useRouter } from 'next/navigation';
import { PiArrowLeft, PiSignOut } from 'react-icons/pi';
import Image from 'next/image';
import logoLight from "@/public/images/logo5.png";
import logoDark from "@/public/images/logo6.png";
import { useTheme } from 'next-themes';
import GradientBackground from '@/components/ui/GradientBackground';

export default function StandaloneFileUploadPage() {
  const router = useRouter();
  const [currentLogo, setCurrentLogo] = useState(logoLight);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const { resolvedTheme } = useTheme();

  // Update logo based on theme
  useEffect(() => {
    setCurrentLogo(resolvedTheme === 'dark' ? logoDark : logoLight);
  }, [resolvedTheme]);

  // Handle logout confirmation
  const handleLogout = () => {
    // Clear session storage
    sessionStorage.removeItem("resultUser");
    localStorage.removeItem("userEmail");

    // Close modal and redirect
    setShowLogoutConfirm(false);
    router.push("/sign-in");
  };

  // Handle logout click
  const handleLogoutClick = () => {
    setShowLogoutConfirm(true);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-n0 text-n500 dark:text-n30 relative">
      <GradientBackground />
      <div className="container mx-auto py-6 px-25  relative z-10">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Image src={currentLogo} alt="QuerryOne Logo" className="mr-1"  />
          </div>

          <div className="flex items-center gap-10">
            <button
              onClick={handleLogoutClick}
              className="flex items-center text-gray-600 dark:text-gray-300 hover:text-primaryColor transition-colors py-2 px-4 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <PiSignOut className="mr-2" />
              Sign Out
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-n0 rounded-xl shadow-sm border border-gray-100 dark:border-gray-800 overflow-hidden">
          <FileUploadPage />
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-n0 p-6 rounded-lg shadow-xl max-w-sm w-full mx-4">
            <h2 className="text-lg font-semibold text-n800 dark:text-n10 mb-4">
              Confirm Logout
            </h2>
            <p className="text-sm text-n600 dark:text-n40 mb-6">
              Are you sure you want to log out? You will need to sign in again to access your account.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowLogoutConfirm(false)}
                className="px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n800 rounded-md hover:bg-gray-300 dark:hover:bg-n700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleLogout}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors flex items-center"
              >
                <PiSignOut className="mr-2" />
                Log Out
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
