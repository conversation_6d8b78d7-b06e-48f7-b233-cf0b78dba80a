"use client";
import React, { useState, useEffect, useMemo } from "react";
import {
  PiTrash,
  PiDatabase,
  PiEnvelope,
  PiWarning,
  PiList,
  PiMagnifyingGlass,
  PiX,
  PiCheck,
  PiCheckSquare,
  PiSquare,
  PiCheckCircle,
  PiInfo,
  PiTag,
  PiShieldCheck,
  PiArrowsClockwise,
  PiCursor,
  PiHandPointing,
  PiPencil,
  PiTable,
  PiDownloadSimple
} from "react-icons/pi";
import AdminSidebar from './adminsidebar';
import { deleteFaissIndex } from '../services/api';
import { getIndexesByEmail, fetchEmails } from '../services/fileUploadService';
// @ts-ignore
import EditDataModal from './modals/EditDataModal';

interface IndexData {
  _id: string;
  email: string;
  index_name: string;
  embed_model?: string;
  api_key?: string;
  source: 'FAISS' | 'PINE';
  [key: string]: any;
}

interface BulkDeleteProgress {
  [itemId: string]: {
    status: 'pending' | 'deleting' | 'success' | 'error';
    message?: string;
  };
}

interface DeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  itemData: IndexData | null;
  selectedItems?: IndexData[];
  isDeleting: boolean;
  isBulkDelete?: boolean;
  bulkProgress?: BulkDeleteProgress;
}

const DeleteConfirmationModal: React.FC<DeleteModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  itemData,
  selectedItems = [],
  isDeleting,
  isBulkDelete = false,
  bulkProgress = {}
}) => {
  if (!isOpen || (!itemData && !isBulkDelete)) return null;

  const isMultiple = isBulkDelete && selectedItems.length > 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-n800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex items-center mb-4">
          <PiWarning className="text-red-500 text-2xl mr-3" />
          <h3 className="text-lg font-semibold text-n700 dark:text-n20">
            {isMultiple ? `Confirm Bulk Deletion (${selectedItems.length} items)` : 'Confirm Deletion'}
          </h3>
        </div>

        <div className="mb-6">
          <p className="text-n600 dark:text-n30 mb-4">
            Are you sure you want to delete {isMultiple ? 'these indexes' : 'this index'}? This action will:
          </p>
          <ul className="list-disc list-inside text-sm text-n500 dark:text-n40 space-y-1">
            <li>Remove the record{isMultiple ? 's' : ''} from PINE collection database</li>
            <li>Delete the entire FAISS index director{isMultiple ? 'ies' : 'y'} and all files</li>
            <li>Permanently remove all vector embeddings and metadata</li>
            <li>This action cannot be undone</li>
          </ul>

          {/* Single item details */}
          {!isMultiple && itemData && (
            <div className="mt-4 p-3 bg-gray-50 dark:bg-n700 rounded-md">
              <p className="text-sm font-medium text-n700 dark:text-n20">
                Category: {itemData.index_name}
              </p>
              <p className="text-sm text-n500 dark:text-n40">
                Email: {itemData.email}
              </p>
              <p className="text-sm text-n500 dark:text-n40">
                Model: {itemData.embed_model || 'N/A'}
              </p>
            </div>
          )}

          {/* Multiple items list */}
          {isMultiple && (
            <div className="mt-4 space-y-2 max-h-60 overflow-y-auto">
              {selectedItems.map((item) => {
                const progress = bulkProgress[item._id];
                return (
                  <div key={item._id} className="p-3 bg-gray-50 dark:bg-n700 rounded-md">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-n700 dark:text-n20">
                          {item.index_name}
                        </p>
                        <p className="text-xs text-n500 dark:text-n40">
                          {item.email}
                        </p>
                      </div>
                      {progress && (
                        <div className="flex items-center ml-2">
                          {progress.status === 'pending' && (
                            <span className="text-xs text-gray-500">Pending</span>
                          )}
                          {progress.status === 'deleting' && (
                            <div className="flex items-center">
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500 mr-1"></div>
                              <span className="text-xs text-blue-600">Deleting...</span>
                            </div>
                          )}
                          {progress.status === 'success' && (
                            <div className="flex items-center">
                              <PiCheck className="text-green-500 text-sm mr-1" />
                              <span className="text-xs text-green-600">Success</span>
                            </div>
                          )}
                          {progress.status === 'error' && (
                            <div className="flex items-center">
                              <PiX className="text-red-500 text-sm mr-1" />
                              <span className="text-xs text-red-600">Error</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    {progress?.message && (
                      <p className="text-xs text-n500 dark:text-n40 mt-1">
                        {progress.message}
                      </p>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={isDeleting}
            className="px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n700 rounded-md hover:bg-gray-300 dark:hover:bg-n600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDeleting ? 'Close' : 'Cancel'}
          </button>
          {!isDeleting && (
            <button
              onClick={onConfirm}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md flex items-center"
            >
              <PiTrash className="mr-2" />
              Delete {isMultiple ? `${selectedItems.length} Items` : 'Item'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

const ShowDeleteIndex: React.FC = () => {
  const [indexData, setIndexData] = useState<IndexData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteModal, setDeleteModal] = useState({
    isOpen: false,
    item: null as IndexData | null
  });
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [emails, setEmails] = useState<string[]>([]);
  const [selectedEmail, setSelectedEmail] = useState<string>("");

  // Multi-select state
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isMultiSelectMode, setIsMultiSelectMode] = useState(false);
  const [bulkDeleteModal, setBulkDeleteModal] = useState({
    isOpen: false,
    items: [] as IndexData[]
  });
  const [bulkDeleteProgress, setBulkDeleteProgress] = useState<BulkDeleteProgress>({});
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);

  // Edit Data Modal state
  const [editDataModal, setEditDataModal] = useState({
    isOpen: false,
    indexData: null as IndexData | null
  });

  // Notification state
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
    isVisible: boolean;
  }>({
    type: 'info',
    message: '',
    isVisible: false
  });

  // Notification helper function
  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message, isVisible: true });
    setTimeout(() => {
      setNotification(prev => ({ ...prev, isVisible: false }));
    }, 5000);
  };

  // Filter data based on search term
  const filteredData = useMemo(() => {
    if (!searchTerm.trim()) {
      return indexData;
    }

    const searchLower = searchTerm.toLowerCase().trim();
    return indexData.filter((item: IndexData) => {
      return (
        item.email.toLowerCase().includes(searchLower) ||
        item.index_name.toLowerCase().includes(searchLower) ||
        (item.embed_model && item.embed_model.toLowerCase().includes(searchLower))
      );
    });
  }, [indexData, searchTerm]);

  // Clear search
  const clearSearch = () => {
    setSearchTerm("");
    showNotification('info', 'Search cleared');
  };

  // Fetch emails and index data
  const fetchIndexData = async () => {
    try {
      setLoading(true);
      setError(null);

      // First, fetch all emails
      const emailList = await fetchEmails();
      setEmails(emailList);

      let allIndexData: IndexData[] = [];

      // If a specific email is selected, fetch indexes for that email
      if (selectedEmail) {
        console.log(`Fetching indexes for selected email: ${selectedEmail}`);
        const emailIndexes = await getIndexesByEmail(selectedEmail);
        allIndexData = [...allIndexData, ...emailIndexes];
      } else {
        // If no email is selected, fetch indexes for all emails
        console.log('Fetching indexes for all emails...');
        for (const email of emailList) {
          try {
            const emailIndexes = await getIndexesByEmail(email);
            allIndexData = [...allIndexData, ...emailIndexes];
          } catch (error) {
            console.error(`Error fetching indexes for email ${email}:`, error);
            // Continue with other emails even if one fails
          }
        }
      }

      console.log('All fetched index data:', allIndexData);
      setIndexData(allIndexData);

      // Show success notification if data was fetched successfully
      if (allIndexData.length > 0) {
        showNotification('success', `Successfully loaded ${allIndexData.length} indexes`);
      } else {
        showNotification('info', 'No indexes found');
      }
    } catch (err) {
      console.error('Error fetching index data:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch data';
      setError(errorMessage);
      showNotification('error', `Failed to load data: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Delete function - deletes both from PINE collection and FAISS directory
  const handleDelete = async (item: IndexData) => {
    try {
      setDeleteLoading(item._id);

      // Use the original _id format if it exists, otherwise use the processed _id
      const resourceId = item._id?.includes('$oid') ? item._id : item._id;
      console.log('Deleting item with resourceId:', resourceId);
      console.log('Full item data:', item);

      let pineDeleteSuccess = false;
      let faissDirectoryDeleteSuccess = false;
      let pineError = null;
      let faissDirectoryError = null;

      // Step 1: Delete from PINE collection
      try {
        console.log('Step 1: Deleting from PINE collection...');
        const pineResponse = await fetch(
          `https://dev-commonmannit.mannit.co/mannit/eDeleteWCol?resourceId=${resourceId}&ColName=PINE`,
          {
            method: 'DELETE',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'xxxid': 'PINE'
            }
          }
        );

        if (pineResponse.ok) {
          console.log('PINE collection entry deleted successfully');
          pineDeleteSuccess = true;
        } else {
          const errorText = await pineResponse.text();
          console.error('PINE collection delete API error:', errorText);
          pineError = `PINE collection deletion failed: ${pineResponse.status} - ${errorText}`;
        }
      } catch (err) {
        console.error('Error deleting from PINE collection:', err);
        pineError = `PINE collection deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;
      }

      // Step 2: Delete FAISS directory (only if we have an index_name)
      if (item.index_name && item.index_name !== 'N/A') {
        try {
          console.log(`Step 2: Deleting FAISS directory for index: ${item.index_name}...`);
          const faissResult = await deleteFaissIndex(item.index_name);
          console.log('FAISS directory delete response:', faissResult);

          if (faissResult.success) {
            faissDirectoryDeleteSuccess = true;
          } else {
            faissDirectoryError = `FAISS directory deletion failed: ${faissResult.error}`;
          }
        } catch (err) {
          console.error('Error deleting FAISS directory:', err);
          faissDirectoryError = `FAISS directory deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;
        }
      } else {
        console.log('Step 2: Skipping FAISS directory deletion - no valid index name');
        faissDirectoryDeleteSuccess = true; // Consider it successful if there's nothing to delete
      }

      // Determine overall success and handle results
      const overallSuccess = pineDeleteSuccess && faissDirectoryDeleteSuccess;

      if (overallSuccess) {
        console.log('✅ Successfully deleted both PINE collection entry and FAISS directory');
        // Remove item from local state
        setIndexData(prevData => prevData.filter(dataItem => dataItem._id !== item._id));
        // Close modal
        setDeleteModal({ isOpen: false, item: null });
        // Show success notification
        showNotification('success', `Successfully deleted index "${item.index_name}"`);
      } else {
        // Partial or complete failure
        let errorMessage = 'Deletion completed with issues:\n';

        if (pineDeleteSuccess) {
          errorMessage += '✅ PINE collection entry deleted successfully\n';
        } else {
          errorMessage += `❌ PINE collection deletion failed: ${pineError}\n`;
        }

        if (faissDirectoryDeleteSuccess) {
          errorMessage += '✅ FAISS directory deleted successfully';
        } else {
          errorMessage += `❌ FAISS directory deletion failed: ${faissDirectoryError}`;
        }

        console.error('Partial deletion result:', errorMessage);

        // If PINE collection was deleted successfully, still remove from UI
        if (pineDeleteSuccess) {
          setIndexData(prevData => prevData.filter(dataItem => dataItem._id !== item._id));
          setDeleteModal({ isOpen: false, item: null });
        }

        // Show error to user
        setError(errorMessage);
        showNotification('error', 'Deletion completed with some issues. Check the details above.');
      }

    } catch (err) {
      console.error('Error in delete process:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete item');
    } finally {
      setDeleteLoading(null);
    }
  };

  // Load data on component mount and when selectedEmail changes
  useEffect(() => {
    fetchIndexData();
  }, [selectedEmail]);

  // Clear selections when data changes
  useEffect(() => {
    setSelectedItems(new Set());
  }, [indexData]);

  // Listen for FAISS index updates
  useEffect(() => {
    const handleFaissIndexUpdate = () => {
      console.log('FAISS index updated, refreshing data...');
      fetchIndexData();
    };

    window.addEventListener('faissIndexUpdated', handleFaissIndexUpdate);

    return () => {
      window.removeEventListener('faissIndexUpdated', handleFaissIndexUpdate);
    };
  }, []);

  const openDeleteModal = (item: IndexData) => {
    setDeleteModal({ isOpen: true, item });
  };

  const closeDeleteModal = () => {
    setDeleteModal({ isOpen: false, item: null });
  };

  const confirmDelete = () => {
    if (deleteModal.item) {
      handleDelete(deleteModal.item);
    }
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Edit Data Modal functions
  const openEditDataModal = (item: IndexData) => {
    setEditDataModal({ isOpen: true, indexData: item });
    showNotification('info', `Loading data for index "${item.index_name}"`);
  };

  const closeEditDataModal = () => {
    setEditDataModal({ isOpen: false, indexData: null });
  };

  // Multi-select functions
  const toggleMultiSelectMode = () => {
    const newMode = !isMultiSelectMode;
    setIsMultiSelectMode(newMode);
    setSelectedItems(new Set());

    if (newMode) {
      showNotification('info', 'Multi-select mode enabled. Click checkboxes to select items for bulk operations.');
    } else {
      showNotification('info', 'Multi-select mode disabled.');
    }
  };

  const toggleItemSelection = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  const selectAllItems = () => {
    const allIds = new Set(filteredData.map(item => item._id));
    setSelectedItems(allIds);
    showNotification('info', `Selected all ${filteredData.length} visible items`);
  };

  const deselectAllItems = () => {
    setSelectedItems(new Set());
    showNotification('info', 'Deselected all items');
  };

  const openBulkDeleteModal = () => {
    const itemsToDelete = filteredData.filter(item => selectedItems.has(item._id));
    setBulkDeleteModal({ isOpen: true, items: itemsToDelete });
  };

  const closeBulkDeleteModal = () => {
    setBulkDeleteModal({ isOpen: false, items: [] });
    setBulkDeleteProgress({});
  };

  // Bulk delete function
  const handleBulkDelete = async () => {
    if (bulkDeleteModal.items.length === 0) return;

    setIsBulkDeleting(true);

    // Initialize progress for all items
    const initialProgress: BulkDeleteProgress = {};
    bulkDeleteModal.items.forEach(item => {
      initialProgress[item._id] = { status: 'pending' };
    });
    setBulkDeleteProgress(initialProgress);

    const deletedItems: string[] = [];

    // Process each item sequentially to avoid overwhelming the server
    for (const item of bulkDeleteModal.items) {
      try {
        // Update status to deleting
        setBulkDeleteProgress(prev => ({
          ...prev,
          [item._id]: { status: 'deleting', message: 'Deleting...' }
        }));

        // Use the same delete logic as single item delete
        const resourceId = item._id?.includes('$oid') ? item._id : item._id;

        let pineDeleteSuccess = false;
        let faissDirectoryDeleteSuccess = false;
        let pineError = null;
        let faissDirectoryError = null;

        // Step 1: Delete from PINE collection
        try {
          const pineResponse = await fetch(
            `https://dev-commonmannit.mannit.co/mannit/eDeleteWCol?resourceId=${resourceId}&ColName=PINE`,
            {
              method: 'DELETE',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'xxxid': 'PINE'
              }
            }
          );

          if (pineResponse.ok) {
            pineDeleteSuccess = true;
          } else {
            const errorText = await pineResponse.text();
            pineError = `PINE deletion failed: ${pineResponse.status} - ${errorText}`;
          }
        } catch (err) {
          pineError = `PINE deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;
        }

        // Step 2: Delete FAISS directory
        if (item.index_name && item.index_name !== 'N/A') {
          try {
            const faissResult = await deleteFaissIndex(item.index_name);
            if (faissResult.success) {
              faissDirectoryDeleteSuccess = true;
            } else {
              faissDirectoryError = `FAISS deletion failed: ${faissResult.error}`;
            }
          } catch (err) {
            faissDirectoryError = `FAISS deletion error: ${err instanceof Error ? err.message : 'Unknown error'}`;
          }
        } else {
          faissDirectoryDeleteSuccess = true;
        }

        // Update progress based on results
        const overallSuccess = pineDeleteSuccess && faissDirectoryDeleteSuccess;

        if (overallSuccess) {
          setBulkDeleteProgress(prev => ({
            ...prev,
            [item._id]: { status: 'success', message: 'Successfully deleted' }
          }));
          deletedItems.push(item._id);
        } else {
          let errorMessage = '';
          if (!pineDeleteSuccess) errorMessage += pineError + ' ';
          if (!faissDirectoryDeleteSuccess) errorMessage += faissDirectoryError;

          setBulkDeleteProgress(prev => ({
            ...prev,
            [item._id]: { status: 'error', message: errorMessage.trim() }
          }));
        }

      } catch (err) {
        setBulkDeleteProgress(prev => ({
          ...prev,
          [item._id]: {
            status: 'error',
            message: err instanceof Error ? err.message : 'Unknown error'
          }
        }));
      }
    }

    // Remove successfully deleted items from the UI
    if (deletedItems.length > 0) {
      setIndexData(prevData => prevData.filter(item => !deletedItems.includes(item._id)));
      setSelectedItems(new Set());
      showNotification('success', `Successfully deleted ${deletedItems.length} of ${bulkDeleteModal.items.length} selected indexes`);
    }

    setIsBulkDeleting(false);

    // Show error notification if some items failed
    const failedCount = bulkDeleteModal.items.length - deletedItems.length;
    if (failedCount > 0) {
      showNotification('error', `${failedCount} items failed to delete. Check the details in the modal.`);
    }

    // Auto-close modal after a delay if all items were successful
    const allSuccessful = bulkDeleteModal.items.every(item =>
      deletedItems.includes(item._id)
    );

    if (allSuccessful) {
      setTimeout(() => {
        closeBulkDeleteModal();
      }, 2000);
    }
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <AdminSidebar
          currentView="show"
          isOpen={sidebarOpen}
          onToggle={toggleSidebar}
        />
        <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`}>
          {/* Mobile header with hamburger */}
          <div className="lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
            <button
              onClick={toggleSidebar}
              className="text-gray-600 dark:text-gray-300 hover:text-primaryColor"
            >
              <PiList className="text-xl" />
            </button>
          </div>
          <div className="container mx-auto px-4 py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primaryColor mx-auto mb-4"></div>
              <p className="text-n600 dark:text-n30">Loading index data...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <AdminSidebar
          currentView="show"
          isOpen={sidebarOpen}
          onToggle={toggleSidebar}
        />
        <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`}>
          {/* Mobile header with hamburger */}
          <div className="lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
            <button
              onClick={toggleSidebar}
              className="text-gray-600 dark:text-gray-300 hover:text-primaryColor"
            >
              <PiList className="text-xl" />
            </button>
          </div>
          <div className="container mx-auto px-4 py-8">
            <div className="text-center">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
                <PiWarning className="text-red-500 text-3xl mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-red-700 dark:text-red-400 mb-2">
                  Error Loading Data
                </h3>
                <p className="text-red-600 dark:text-red-300 mb-4">{error}</p>
                <button
                  onClick={fetchIndexData}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <AdminSidebar
        currentView="show"
        isOpen={sidebarOpen}
        onToggle={toggleSidebar}
      />
      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-64'}`}>
        {/* Mobile header with hamburger */}
        <div className="lg:hidden bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
          <button
            onClick={toggleSidebar}
            className="text-gray-600 dark:text-gray-300 hover:text-primaryColor"
          >
            <PiList className="text-xl" />
          </button>
        </div>
        <div className="flex-1 overflow-auto">
          <div className="container mx-auto px-4 py-8">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-n700 dark:text-n20 mb-4">
                Index Management
              </h1>
              <p className="text-n600 dark:text-n30 max-w-2xl mx-auto">
                Manage your FAISS indexes stored in PINE collection. Deleting an index will remove both the PINE collection entry and the entire FAISS directory with all associated files.
              </p>
            </div>

            {/* Email Filter */}
            <div className="mb-6">
              <div className="max-w-md mx-auto">
                <label htmlFor="email-filter" className="block text-sm font-medium text-n700 dark:text-n20 mb-2">
                  Filter by Email:
                </label>
                <select
                  id="email-filter"
                  value={selectedEmail}
                  onChange={(e) => setSelectedEmail(e.target.value)}
                  className="w-full p-3 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20"
                >
                  <option value="">-- All Emails --</option>
                  {emails.map((email, index) => (
                    <option key={index} value={email}>
                      {email}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Search Bar */}
            <div className="mb-6">
              <div className="max-w-md mx-auto relative">
                <div className="relative">
                  <PiMagnifyingGlass className="absolute left-3 top-1/2 transform -translate-y-1/2 text-n400 dark:text-n500" />
                  <input
                    type="text"
                    placeholder="Search by email, index name, or embedding model..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-n600 rounded-lg focus:ring-2 focus:ring-primaryColor focus:border-transparent bg-white dark:bg-n800 text-n700 dark:text-n20 placeholder-n400 dark:placeholder-n500"
                  />
                  {searchTerm && (
                    <button
                      onClick={clearSearch}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-n400 dark:text-n500 hover:text-n600 dark:hover:text-n300 transition-colors"
                    >
                      <PiX />
                    </button>
                  )}
                </div>
                {searchTerm && (
                  <div className="mt-2 text-sm text-n500 dark:text-n40 text-center">
                    Found {filteredData.length} result{filteredData.length !== 1 ? 's' : ''} for "{searchTerm}"
                  </div>
                )}
              </div>
            </div>

            {/* Control Panel */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
              {/* Multi-select controls */}
              <div className="flex items-center gap-4">
                <button
                  onClick={toggleMultiSelectMode}
                  className={`px-4 py-2 rounded-md transition-all duration-200 flex items-center font-medium ${
                    isMultiSelectMode
                      ? 'bg-blue-500 text-white hover:bg-blue-600 shadow-md'
                      : 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700 hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-800/30 dark:hover:to-indigo-800/30 hover:shadow-sm'
                  }`}
                >
                  {isMultiSelectMode ? (
                    <>
                      <PiX className="mr-2 w-4 h-4" />
                      Exit Multi-Select
                    </>
                  ) : (
                    <>
                      <PiCursor className="mr-2 w-4 h-4" />
                      Enable Multi-Select
                    </>
                  )}
                </button>

                {isMultiSelectMode && filteredData.length > 0 && (
                  <>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={selectedItems.size === filteredData.length ? deselectAllItems : selectAllItems}
                        className="text-sm px-3 py-1 rounded-md bg-primaryColor/10 text-primaryColor hover:bg-primaryColor/20 transition-colors flex items-center gap-1 font-medium"
                      >
                        {selectedItems.size === filteredData.length ? (
                          <>
                            <PiSquare className="w-3 h-3" />
                            Deselect All
                          </>
                        ) : (
                          <>
                            <PiHandPointing className="w-3 h-3" />
                            Select All
                          </>
                        )}
                      </button>
                      {selectedItems.size > 0 && (
                        <span className="text-sm text-n500 dark:text-n40">
                          ({selectedItems.size} selected)
                        </span>
                      )}
                    </div>

                    {selectedItems.size > 0 && (
                      <button
                        onClick={openBulkDeleteModal}
                        className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center"
                      >
                        <PiTrash className="mr-2" />
                        Delete Selected ({selectedItems.size})
                      </button>
                    )}
                  </>
                )}
              </div>

              {/* Export and Refresh buttons */}
              <div className="flex gap-2">
                {indexData.length > 0 && (
                  <button
                    onClick={() => {
                      const dataToExport = filteredData.map(item => ({
                        email: item.email,
                        index_name: item.index_name,
                        embed_model: item.embed_model || 'N/A',
                        source: item.source
                      }));

                      const csvContent = [
                        'Email,Index Name,Embedding Model,Source',
                        ...dataToExport.map(item =>
                          `"${item.email}","${item.index_name}","${item.embed_model}","${item.source}"`
                        )
                      ].join('\n');

                      const blob = new Blob([csvContent], { type: 'text/csv' });
                      const url = URL.createObjectURL(blob);
                      const link = document.createElement('a');
                      link.href = url;
                      link.download = `indexes_export_${new Date().toISOString().split('T')[0]}.csv`;
                      link.click();
                      URL.revokeObjectURL(url);

                      showNotification('success', `Exported ${dataToExport.length} indexes to CSV`);
                    }}
                    className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center"
                  >
                    <PiDownloadSimple className="mr-2" />
                    Export CSV
                  </button>
                )}

                <button
                  onClick={fetchIndexData}
                  disabled={loading}
                  className="px-4 py-2 bg-primaryColor text-white rounded-md hover:bg-primaryColor/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Loading...
                    </>
                  ) : (
                    <>
                      <PiArrowsClockwise className="mr-2" />
                      Refresh Data
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Data Grid */}
            {indexData.length === 0 ? (
              <div className="text-center py-12">
                <PiDatabase className="text-6xl text-n300 dark:text-n600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-n600 dark:text-n30 mb-2">
                  No Data Found
                </h3>
                <p className="text-n500 dark:text-n40">
                  No indexes found in the collections.
                </p>
              </div>
            ) : filteredData.length === 0 ? (
              <div className="text-center py-12">
                <PiMagnifyingGlass className="text-6xl text-n300 dark:text-n600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-n600 dark:text-n30 mb-2">
                  No Results Found
                </h3>
                <p className="text-n500 dark:text-n40 mb-4">
                  No entries match your search term "{searchTerm}".
                </p>
                <button
                  onClick={clearSearch}
                  className="px-4 py-2 bg-primaryColor text-white rounded-md hover:bg-primaryColor/90 transition-colors"
                >
                  Clear Search
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredData.map((item) => (
                  <div
                    key={item._id}
                    className={`border rounded-lg overflow-hidden hover:shadow-md transition-all duration-300 bg-white dark:bg-n800 ${
                      isMultiSelectMode && selectedItems.has(item._id)
                        ? 'border-blue-500 ring-2 ring-blue-200 dark:ring-blue-800'
                        : 'border-primaryColor/20 hover:border-primaryColor/50'
                    }`}
                  >
                    <div className="p-6">
                      {/* Header */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          {isMultiSelectMode && (
                            <button
                              onClick={() => toggleItemSelection(item._id)}
                              className="mr-3 p-1 rounded hover:bg-gray-100 dark:hover:bg-n700 transition-colors"
                            >
                              {selectedItems.has(item._id) ? (
                                <PiCheckSquare className="text-blue-500 text-lg" />
                              ) : (
                                <PiSquare className="text-n400 dark:text-n500 text-lg" />
                              )}
                            </button>
                          )}
                          <PiDatabase className="text-primaryColor text-xl mr-2" />
                          <h3 className="font-semibold text-n700 dark:text-n20">
                            Index Entry
                          </h3>
                        </div>
                        <div className="flex gap-2">
                          <span className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 flex items-center gap-1">
                            <PiShieldCheck className="w-3 h-3" />
                            PINE
                          </span>
                          <span className="text-xs bg-primaryColor/10 text-primaryColor px-2 py-1 rounded-full flex items-center gap-1">
                            <PiCheckCircle className="w-3 h-3" />
                            Active
                          </span>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="space-y-3 mb-6">
                        <div className="flex items-start">
                          <PiEnvelope className="text-green-500 dark:text-green-400 mt-1 mr-3 flex-shrink-0" />
                          <div>
                            <p className="text-xs text-n500 dark:text-n40 uppercase tracking-wide">
                              Email
                            </p>
                            <p className="text-sm font-medium text-n700 dark:text-n20 break-all">
                              {item.email}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <PiTag className="text-blue-500 dark:text-blue-400 mt-1 mr-3 flex-shrink-0" />
                          <div>
                            <p className="text-xs text-n500 dark:text-n40 uppercase tracking-wide">
                              Index Name
                            </p>
                            <p className="text-sm font-medium text-n700 dark:text-n20 break-all">
                              {item.index_name}
                            </p>
                          </div>
                        </div>

                        {item.embed_model && (
                          <div className="flex items-start">
                            <PiInfo className="text-purple-500 dark:text-purple-400 mt-1 mr-3 flex-shrink-0" />
                            <div>
                              <p className="text-xs text-n500 dark:text-n40 uppercase tracking-wide">
                                Embedding Model
                              </p>
                              <p className="text-sm font-mono text-n700 dark:text-n20 break-all">
                                {item.embed_model}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Action Buttons - Hidden in multi-select mode */}
                      {!isMultiSelectMode && (
                        <div className="space-y-3">
                          <button
                            onClick={() => openEditDataModal(item)}
                            className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors font-medium flex items-center justify-center"
                          >
                            <PiTable className="mr-2" />
                            Edit Data
                          </button>
                          <button
                            onClick={() => openDeleteModal(item)}
                            disabled={deleteLoading === item._id}
                            className="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:bg-gray-300 dark:disabled:bg-gray-700 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center"
                          >
                            {deleteLoading === item._id ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Deleting...
                              </>
                            ) : (
                              <>
                                <PiTrash className="mr-2" />
                                Delete Index
                              </>
                            )}
                          </button>
                        </div>
                      )}

                      {/* Multi-select info */}
                      {isMultiSelectMode && (
                        <div className="text-center text-sm text-n500 dark:text-n40">
                          {selectedItems.has(item._id) ? 'Selected for deletion' : 'Click checkbox to select'}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Delete Confirmation Modal */}
            <DeleteConfirmationModal
              isOpen={deleteModal.isOpen}
              onClose={closeDeleteModal}
              onConfirm={confirmDelete}
              itemData={deleteModal.item}
              isDeleting={deleteLoading !== null}
            />

            {/* Bulk Delete Modal */}
            <DeleteConfirmationModal
              isOpen={bulkDeleteModal.isOpen}
              onClose={closeBulkDeleteModal}
              onConfirm={handleBulkDelete}
              itemData={null}
              selectedItems={bulkDeleteModal.items}
              isDeleting={isBulkDeleting}
              isBulkDelete={true}
              bulkProgress={bulkDeleteProgress}
            />

            {/* Edit Data Modal */}
            <EditDataModal
              isOpen={editDataModal.isOpen}
              onClose={closeEditDataModal}
              indexData={editDataModal.indexData}
            />

            {/* Notification Toast */}
            {notification.isVisible && (
              <div className={`fixed top-4 right-4 z-50 max-w-md p-4 rounded-lg shadow-lg transition-all duration-300 transform ${
                notification.isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
              } ${
                notification.type === 'success'
                  ? 'bg-green-500 text-white'
                  : notification.type === 'error'
                  ? 'bg-red-500 text-white'
                  : 'bg-blue-500 text-white'
              }`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {notification.type === 'success' && <PiCheckCircle className="mr-2 text-lg" />}
                    {notification.type === 'error' && <PiWarning className="mr-2 text-lg" />}
                    {notification.type === 'info' && <PiInfo className="mr-2 text-lg" />}
                    <span className="text-sm font-medium">{notification.message}</span>
                  </div>
                  <button
                    onClick={() => setNotification(prev => ({ ...prev, isVisible: false }))}
                    className="ml-4 text-white hover:text-gray-200 transition-colors"
                  >
                    <PiX className="text-lg" />
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShowDeleteIndex;