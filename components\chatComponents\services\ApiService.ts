import { CacheService } from './CacheService';

// API configuration
export const API_CONFIG = {
  // Production endpoint (previously used)
  PROD_ENDPOINT: "http://localhost:5010/financial_query",
  // Development endpoint for suggest.py (running locally)
  DEV_ENDPOINT: "http://localhost:5010/financial_query",
  // Use DEV_ENDPOINT for local development, PROD_ENDPOINT for production
  ACTIVE_ENDPOINT: "http://localhost:5010/financial_query",
  // FAISS collection endpoint (keeping PINE for backward compatibility)
  PINE_COLLECTION_ENDPOINT: "https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS"
};

export interface ApiRequestBody {
  query: string;
  client_email?: string;
  index_name?: string;
  embed_model?: string;
  upload_context?: string;
  has_recent_uploads?: boolean;
  target_language?: string;
  enable_translation?: boolean;
}

export interface ApiResponse {
  ai_response: string;
  related_questions?: string[];
  pinecone_indexes?: string[];
  sentence_analysis?: Array<{ sentence: string; url: string; summary?: string }>;
  faiss_categories?: any[];
  has_uploaded_content?: boolean;
  upload_sources?: any[];
  error?: string;
  translation_applied?: boolean;
  query_language?: string;
  target_language?: string;
  translation_metadata?: any;
  source_language?: string;
  translation_timestamp?: string;
}

export class ApiService {

  // Function to fetch PINE collection data for the current user
  // Falls back to default configuration (default index) when:
  // - No user email is found
  // - API requests fail
  // - No PINE data exists for user
  // - Any errors occur
  static async fetchPineCollection() {
    try {
      const userEmail = localStorage.getItem("user_email");
      console.log("local mail", userEmail);
      if (!userEmail) {
        console.warn("No user email found in localStorage - using default configuration");
        // Set default configuration
        const defaultConfig = {
          api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
          index_name: "default",
          embed_model: "all-MiniLM-L6-v2"
        };
        localStorage.setItem("faiss_index_name", defaultConfig.index_name);
        if (defaultConfig.embed_model) {
          localStorage.setItem("faiss_embed_model", defaultConfig.embed_model);
        }
        return defaultConfig;
      }

      // Use filtered API endpoint to get ALL entries for this user (remove filtercount=1 to get all matches)
      const filterUrl = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(userEmail.trim())}`;

      const response = await fetch(filterUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'xxxid': 'FAISS'
        }
      });

      if (!response.ok) {
        console.warn(`Failed to fetch PINE collection: ${response.status} - using default configuration`);
        // Set default FAISS configuration
        const defaultConfig = {
          index_name: "default",
          embed_model: "all-MiniLM-L6-v2"
        };
        localStorage.setItem("faiss_index_name", defaultConfig.index_name);
        localStorage.setItem("faiss_embed_model", defaultConfig.embed_model);
        return defaultConfig;
      }

      const data = await response.json();
      console.log("FAISS collection response:", data);

      // Check if user exists in FAISS collection and extract all their indexes
      if (data.statusCode === 200 && data.source && data.source.length > 0) {
        // Parse each item in the source array (they are JSON strings)
        const faissData = data.source.map((item: string) => JSON.parse(item));

        // Extract all indexes and embedding models for this user
        const userIndexes: string[] = [];
        const userEmbedModels: string[] = [];
        let firstUserEntry: any | null = null;

        faissData.forEach((item: any) => {
          if (item.client && item.client.trim().toLowerCase() === userEmail.trim().toLowerCase()) {
            if (!firstUserEntry) firstUserEntry = item; // Keep first entry for return
            if (item.index_name && !userIndexes.includes(item.index_name)) {
              userIndexes.push(item.index_name);
            }
            if (item.embed_model && !userEmbedModels.includes(item.embed_model)) {
              userEmbedModels.push(item.embed_model);
            }
          }
        });

        if (userIndexes.length > 0) {
          // User exists in FAISS collection - store all their configurations
          localStorage.setItem("faiss_index_name", userIndexes[0]); // Store first index as default
          localStorage.setItem("faiss_embed_model", userEmbedModels[0] || "all-MiniLM-L6-v2"); // Store first embed model as default
          localStorage.setItem('userFaissIndexes', JSON.stringify(userIndexes)); // Store all indexes
          localStorage.setItem('userEmbedModels', JSON.stringify(userEmbedModels)); // Store all embed models
          console.log("Found existing FAISS data for user:", userEmail);
          console.log("User indexes:", userIndexes);
          console.log("User embed models:", userEmbedModels);
          return firstUserEntry;
        } else {
          // User doesn't exist in FAISS collection - use default values without auto-creation
          console.log("No FAISS data found for user:", userEmail, "- using default configuration without auto-creation");

          const defaultConfig = {
            index_name: "default",
            embed_model: "all-MiniLM-L6-v2"
          };
          localStorage.setItem("faiss_index_name", defaultConfig.index_name);
          localStorage.setItem("faiss_embed_model", defaultConfig.embed_model);
          localStorage.setItem("faiss_client_email", userEmail);
          console.log("Using default FAISS configuration for user:", userEmail);
          return defaultConfig;
        }
      } else {
        // No FAISS data found - use default values without auto-creation
        console.log("No FAISS data found for user:", userEmail, "- using default configuration without auto-creation");

        const defaultConfig = {
          index_name: "default",
          embed_model: "all-MiniLM-L6-v2"
        };
        localStorage.setItem("faiss_index_name", defaultConfig.index_name);
        localStorage.setItem("faiss_embed_model", defaultConfig.embed_model);
        localStorage.setItem("faiss_client_email", userEmail);
        console.log("Using default FAISS configuration for user:", userEmail);
        return defaultConfig;
      }
    } catch (error) {
      console.warn("Error fetching FAISS collection - using default configuration:", error);
      // Fallback to default values on error
      const defaultConfig = {
        index_name: "default",
        embed_model: "all-MiniLM-L6-v2"
      };
      localStorage.setItem("faiss_index_name", defaultConfig.index_name);
      localStorage.setItem("faiss_embed_model", defaultConfig.embed_model);
      return defaultConfig;
    }
  }

  // Function to fetch all available FAISS indexes from the backend
  static async fetchUserIndexes(): Promise<string[]> {
    try {
      console.log("Fetching available FAISS indexes...");

      // Call the list-faiss-indexes endpoint to get all available indexes
      const response = await fetch('http://localhost:5010/api/list-faiss-indexes', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.indexes && data.indexes.length > 0) {
          console.log("✅ Retrieved FAISS indexes:", data.indexes);
          return data.indexes;
        } else {
          console.warn("⚠️ No FAISS indexes found:", data.error || "Unknown error");
        }
      } else {
        console.warn("⚠️ FAISS indexes API failed:", response.status);
      }

      // Fallback to default if API fails
      console.warn("⚠️ Falling back to default index");
      return ["default"];
    } catch (error) {
      console.error("Error fetching FAISS indexes:", error);
      return ["default"];
    }
  }

  // Function to send query to API
  static async sendQuery(requestBody: ApiRequestBody): Promise<ApiResponse> {
    const response = await fetch(API_CONFIG.ACTIVE_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      // Check if the error is related to the index
      if (response.status === 404) {
        const errorData = await response.json();
        if (errorData.error && errorData.error.includes("No matching documents found or index not available")) {
          throw new Error(`The selected index is not available or contains no relevant data. Please try another index.`);
        }
      }
      throw new Error(`API response error: ${response.status}`);
    }

    return await response.json();
  }
}
