import os
import sqlite3
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
import csv
import io
import re

# Database configuration
DB_PATH = os.getenv("DB_PATH", "csv_data.db")

def get_connection():
    """
    Get a connection to the SQLite database.

    Returns:
        sqlite3.Connection: A connection to the SQLite database
    """
    conn = sqlite3.connect(DB_PATH)
    # Enable foreign key support
    conn.execute("PRAGMA foreign_keys = ON")
    return conn

def init_db():
    """
    Initialize the database with necessary tables.
    """
    conn = get_connection()
    cursor = conn.cursor()

    # Check if csv_files table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='csv_files'")
    table_exists = cursor.fetchone()

    if table_exists:
        # Check if columns_json column exists
        cursor.execute("PRAGMA table_info(csv_files)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        # If columns_json doesn't exist, add it
        if 'columns_json' not in column_names:
            cursor.execute("ALTER TABLE csv_files ADD COLUMN columns_json TEXT")
            print("Added columns_json column to csv_files table")

        # Create PINE collection table for FAISS file tracking
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS pine_collection (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            index_name TEXT NOT NULL,
            email TEXT,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(index_name, email)
        )
        ''')
        print("Created pine_collection table for FAISS tracking")
    else:
        # Create a table to track CSV files
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS csv_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_name TEXT NOT NULL,
            index_name TEXT NOT NULL,
            client_email TEXT,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            columns_json TEXT,
            row_count INTEGER,
            embedding_model TEXT,
            embedding_dimension INTEGER,
            UNIQUE(index_name)
        )
        ''')
        print("Created csv_files table")

        # Create a table to track Excel files
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS excel_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_name TEXT NOT NULL,
            index_name TEXT NOT NULL,
            client_id TEXT NOT NULL,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            columns_json TEXT,
            row_count INTEGER,
            embedding_model TEXT,
            embedding_dimension INTEGER,
            detected_language TEXT,
            chunks_created INTEGER,
            UNIQUE(client_id, file_name, index_name)
        )
        ''')
        print("Created excel_files table")

        # Create PINE collection table for FAISS file tracking
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS pine_collection (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            index_name TEXT NOT NULL,
            email TEXT,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(index_name, email)
        )
        ''')
        print("Created pine_collection table for FAISS tracking")

    conn.commit()
    conn.close()

def sanitize_column_name(name: str) -> str:
    """
    Sanitize column names to be valid SQLite column names.

    Args:
        name: The original column name

    Returns:
        str: A sanitized column name
    """
    # Replace spaces and special characters with underscores
    sanitized = re.sub(r'[^a-zA-Z0-9_]', '_', name)
    # Ensure it doesn't start with a number
    if sanitized[0].isdigit():
        sanitized = 'col_' + sanitized
    # Ensure it's not empty
    if not sanitized:
        sanitized = 'column'
    return sanitized

def get_sqlite_type(pandas_dtype):
    """
    Convert pandas dtype to SQLite type.

    Args:
        pandas_dtype: Pandas data type

    Returns:
        str: Corresponding SQLite data type
    """
    if pd.api.types.is_integer_dtype(pandas_dtype):
        return "INTEGER"
    elif pd.api.types.is_float_dtype(pandas_dtype):
        return "REAL"
    elif pd.api.types.is_bool_dtype(pandas_dtype):
        return "INTEGER"  # SQLite doesn't have a boolean type
    else:
        return "TEXT"  # Default to TEXT for strings and other types

def create_table_from_csv(csv_data: str, index_name: str, client_email: Optional[str] = None,
                      embedding_model: Optional[str] = None, embedding_dimension: Optional[int] = None) -> Tuple[bool, str, Dict[str, Any]]:
    """
    Create a database table dynamically based on CSV structure.

    Args:
        csv_data: CSV data as string
        index_name: Name to use for the table (derived from file name)
        client_email: Client email for tracking

    Returns:
        Tuple[bool, str, Dict]: Success status, message, and additional info
    """
    try:
        # Try different parsing options for the CSV
        try:
            # First attempt with default settings
            df = pd.read_csv(io.StringIO(csv_data))
            print(f"Database: CSV parsed successfully with default settings. Columns: {df.columns.tolist()}")
        except Exception as e1:
            print(f"Database: First parsing attempt failed: {e1}")
            try:
                # Second attempt with index_col=0 to handle the first unnamed column
                df = pd.read_csv(io.StringIO(csv_data), index_col=0)
                print(f"Database: CSV parsed successfully with index_col=0. Columns: {df.columns.tolist()}")
            except Exception as e2:
                print(f"Database: Second parsing attempt failed: {e2}")
                # Third attempt with more options
                df = pd.read_csv(io.StringIO(csv_data), escapechar='\\', quotechar='"', doublequote=True)
                print(f"Database: CSV parsed successfully with escape options. Columns: {df.columns.tolist()}")

        # Print sample data for debugging
        print(f"Database: DataFrame shape: {df.shape}")
        if not df.empty:
            print(f"Database: First row sample: {df.iloc[0].to_dict()}")

        # Get column names and types
        columns = []
        column_types = {}
        for col in df.columns:
            if pd.isna(col) or col == '':
                # Skip unnamed columns
                continue

            sanitized_col = sanitize_column_name(col)
            sqlite_type = get_sqlite_type(df[col].dtype)
            columns.append((sanitized_col, sqlite_type, col))  # Store original name too
            column_types[sanitized_col] = {"type": sqlite_type, "original_name": col}

        # Create table name (use index_name as base)
        table_name = f"csv_{sanitize_column_name(index_name)}"

        conn = get_connection()
        cursor = conn.cursor()

        # Check if table already exists
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        if cursor.fetchone():
            # Drop existing table if it exists
            cursor.execute(f"DROP TABLE IF EXISTS {table_name}")

        # Create the table with dynamic columns
        create_table_sql = f"CREATE TABLE {table_name} (id INTEGER PRIMARY KEY AUTOINCREMENT"
        for col_name, col_type, _ in columns:
            create_table_sql += f", {col_name} {col_type}"
        create_table_sql += ")"

        cursor.execute(create_table_sql)

        # Insert data into the table
        for _, row in df.iterrows():
            placeholders = ", ".join(["?"] * len(columns))
            col_names = ", ".join([col_name for col_name, _, _ in columns])

            insert_sql = f"INSERT INTO {table_name} ({col_names}) VALUES ({placeholders})"

            values = [row[orig_name] for _, _, orig_name in columns]
            cursor.execute(insert_sql, values)

        # Record the CSV file in the tracking table
        cursor.execute('''
        INSERT OR REPLACE INTO csv_files
        (file_name, index_name, client_email, columns_json, row_count, embedding_model, embedding_dimension)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            f"{index_name}.csv",
            index_name,
            client_email,
            str(column_types),  # Store column info as string
            len(df),
            embedding_model,
            embedding_dimension
        ))

        # Also record in PINE collection for FAISS tracking
        cursor.execute('''
        INSERT OR REPLACE INTO pine_collection
        (index_name, email)
        VALUES (?, ?)
        ''', (
            index_name,  # Store as relative path to FAISS files
            client_email
        ))

        conn.commit()
        conn.close()

        return True, f"Successfully created table {table_name} with {len(df)} rows", {
            "table_name": table_name,
            "row_count": len(df),
            "columns": column_types
        }

    except Exception as e:
        return False, f"Error creating table: {str(e)}", {}

def list_csv_files(client_email: Optional[str] = None) -> Tuple[bool, str, List[Dict[str, Any]]]:
    """
    List all CSV files stored in the database.

    Args:
        client_email: Optional filter by client email

    Returns:
        Tuple[bool, str, List[Dict]]: Success status, message, and list of files
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        if client_email:
            cursor.execute(
                "SELECT id, file_name, index_name, client_email, upload_date, row_count FROM csv_files WHERE client_email = ? ORDER BY upload_date DESC",
                (client_email,)
            )
        else:
            cursor.execute(
                "SELECT id, file_name, index_name, client_email, upload_date, row_count FROM csv_files ORDER BY upload_date DESC"
            )

        rows = cursor.fetchall()

        result = []
        for row in rows:
            result.append({
                "id": row[0],
                "file_name": row[1],
                "index_name": row[2],
                "client_email": row[3],
                "upload_date": row[4],
                "row_count": row[5]
            })

        conn.close()

        return True, f"Retrieved {len(result)} CSV files", result

    except Exception as e:
        return False, f"Error listing CSV files: {str(e)}", []

def query_csv_data(index_name: str, limit: int = 100, offset: int = 0) -> Tuple[bool, str, Dict[str, Any]]:
    """
    Query data from a CSV table.

    Args:
        index_name: The index name of the CSV file
        limit: Maximum number of rows to return
        offset: Number of rows to skip

    Returns:
        Tuple[bool, str, Dict]: Success status, message, and data
    """
    try:
        table_name = f"csv_{sanitize_column_name(index_name)}"

        conn = get_connection()
        cursor = conn.cursor()

        # Check if table exists
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        if not cursor.fetchone():
            return False, f"Table for {index_name} does not exist", {}

        # Get column information
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cursor.fetchall()]

        # Query data
        cursor.execute(f"SELECT * FROM {table_name} LIMIT ? OFFSET ?", (limit, offset))
        rows = cursor.fetchall()

        # Get total count
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        total_count = cursor.fetchone()[0]

        # Format as list of dictionaries
        result = []
        for row in rows:
            row_dict = {}
            for i, col in enumerate(columns):
                row_dict[col] = row[i]
            result.append(row_dict)

        conn.close()

        return True, f"Retrieved {len(rows)} rows from {table_name}", {
            "data": result,
            "columns": columns,
            "total_count": total_count,
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        return False, f"Error querying data: {str(e)}", {}

def list_pine_categories(email: Optional[str] = None) -> Tuple[bool, str, List[Dict[str, Any]]]:
    """
    List all categories (index names) from PINE collection for UI display with enhanced email filtering.

    Args:
        email: Optional filter by email (case-insensitive)

    Returns:
        Tuple[bool, str, List[Dict]]: Success status, message, and list of categories
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        if email:
            # Enhanced email filtering with case-insensitive comparison and validation
            email_normalized = email.strip().lower()
            if not email_normalized:
                return False, "Invalid email provided", []

            cursor.execute(
                "SELECT index_name, email, upload_date FROM pine_collection WHERE LOWER(TRIM(email)) = ? ORDER BY upload_date DESC",
                (email_normalized,)
            )
        else:
            cursor.execute(
                "SELECT index_name, email, upload_date FROM pine_collection ORDER BY upload_date DESC"
            )

        rows = cursor.fetchall()

        result = []
        for row in rows:
            result.append({
                "category": row[0],  # Display index_name as category
                "index_name": row[0],
                "email": row[1],
                "upload_date": row[2]
            })

        conn.close()

        return True, f"Retrieved {len(result)} categories", result

    except Exception as e:
        return False, f"Error listing categories: {str(e)}", []


def get_user_pine_indices(email: str) -> Tuple[bool, str, List[str]]:
    """
    Get all PINE collection indices/records that belong to a specific user.

    Args:
        email: User email to filter by

    Returns:
        Tuple[bool, str, List[str]]: Success status, message, and list of index names
    """
    try:
        if not email or not email.strip():
            return False, "Email is required", []

        conn = get_connection()
        cursor = conn.cursor()

        # Case-insensitive email matching with proper validation
        email_normalized = email.strip().lower()

        cursor.execute(
            "SELECT DISTINCT index_name FROM pine_collection WHERE LOWER(TRIM(email)) = ? ORDER BY index_name",
            (email_normalized,)
        )

        rows = cursor.fetchall()
        indices = [row[0] for row in rows if row[0]]  # Filter out any null/empty index names

        conn.close()

        return True, f"Found {len(indices)} indices for user {email}", indices

    except Exception as e:
        return False, f"Error retrieving user indices: {str(e)}", []


def validate_user_access_to_index(email: str, index_name: str) -> Tuple[bool, str]:
    """
    Validate that a user has access to a specific index in the PINE collection.

    Args:
        email: User email
        index_name: Index name to validate access for

    Returns:
        Tuple[bool, str]: Access granted status and message
    """
    try:
        if not email or not email.strip():
            return False, "Email is required"

        if not index_name or not index_name.strip():
            return False, "Index name is required"

        conn = get_connection()
        cursor = conn.cursor()

        # Case-insensitive email matching
        email_normalized = email.strip().lower()
        index_normalized = index_name.strip()

        cursor.execute(
            "SELECT COUNT(*) FROM pine_collection WHERE LOWER(TRIM(email)) = ? AND index_name = ?",
            (email_normalized, index_normalized)
        )

        count = cursor.fetchone()[0]
        conn.close()

        if count > 0:
            return True, f"User {email} has access to index {index_name}"
        else:
            return False, f"User {email} does not have access to index {index_name}"

    except Exception as e:
        return False, f"Error validating user access: {str(e)}"

def add_pine_category(index_name: str, email: str) -> Tuple[bool, str]:
    """
    Add a new category to PINE collection.

    Args:
        index_name: The index name (category)
        email: User email

    Returns:
        Tuple[bool, str]: Success status and message
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute('''
        INSERT OR REPLACE INTO pine_collection
        (index_name, email)
        VALUES (?, ?)
        ''', (index_name, email))

        conn.commit()
        conn.close()

        return True, f"Successfully added category: {index_name}"

    except Exception as e:
        return False, f"Error adding category: {str(e)}"

def remove_pine_category(index_name: str, email: str) -> Tuple[bool, str]:
    """
    Remove a category from PINE collection.

    Args:
        index_name: The index name (category)
        email: User email

    Returns:
        Tuple[bool, str]: Success status and message
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute('''
        DELETE FROM pine_collection
        WHERE index_name = ? AND email = ?
        ''', (index_name, email))

        conn.commit()
        conn.close()

        return True, f"Successfully removed category: {index_name}"

    except Exception as e:
        return False, f"Error removing category: {str(e)}"

def record_excel_upload(
    file_name: str,
    index_name: str,
    client_id: str,
    columns_json: str,
    row_count: int,
    embedding_model: str,
    embedding_dimension: int,
    detected_language: str,
    chunks_created: int
) -> Tuple[bool, str]:
    """
    Record Excel file upload in the database.

    Args:
        file_name: Name of the Excel file
        index_name: FAISS index name
        client_id: Client identifier
        columns_json: JSON string of column information
        row_count: Number of rows processed
        embedding_model: Embedding model used
        embedding_dimension: Dimension of embeddings
        detected_language: Detected language (Tamil/English)
        chunks_created: Number of chunks created

    Returns:
        Tuple[bool, str]: Success status and message
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute('''
        INSERT OR REPLACE INTO excel_files
        (file_name, index_name, client_id, columns_json, row_count,
         embedding_model, embedding_dimension, detected_language, chunks_created)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            file_name,
            index_name,
            client_id,
            columns_json,
            row_count,
            embedding_model,
            embedding_dimension,
            detected_language,
            chunks_created
        ))

        # Also record in PINE collection for FAISS tracking
        cursor.execute('''
        INSERT OR REPLACE INTO pine_collection
        (index_name, email)
        VALUES (?, ?)
        ''', (
            index_name,
            client_id  # Using client_id as email for consistency
        ))

        conn.commit()
        conn.close()

        return True, f"Successfully recorded Excel upload: {file_name}"

    except Exception as e:
        return False, f"Error recording Excel upload: {str(e)}"

def list_excel_files(client_id: Optional[str] = None) -> Tuple[bool, str, List[Dict[str, Any]]]:
    """
    List all Excel files stored in the database.

    Args:
        client_id: Optional filter by client ID

    Returns:
        Tuple[bool, str, List[Dict]]: Success status, message, and list of files
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        if client_id:
            cursor.execute(
                """SELECT id, file_name, index_name, client_id, upload_date,
                   row_count, detected_language, chunks_created
                   FROM excel_files WHERE client_id = ? ORDER BY upload_date DESC""",
                (client_id,)
            )
        else:
            cursor.execute(
                """SELECT id, file_name, index_name, client_id, upload_date,
                   row_count, detected_language, chunks_created
                   FROM excel_files ORDER BY upload_date DESC"""
            )

        rows = cursor.fetchall()

        result = []
        for row in rows:
            result.append({
                "id": row[0],
                "file_name": row[1],
                "index_name": row[2],
                "client_id": row[3],
                "upload_date": row[4],
                "row_count": row[5],
                "detected_language": row[6],
                "chunks_created": row[7]
            })

        conn.close()

        return True, f"Retrieved {len(result)} Excel files", result

    except Exception as e:
        return False, f"Error listing Excel files: {str(e)}", []

def check_excel_file_exists(client_id: str, file_name: str, index_name: str) -> Tuple[bool, str, bool]:
    """
    Check if an Excel file has already been uploaded by a client.

    Args:
        client_id: Client identifier
        file_name: Excel file name
        index_name: FAISS index name

    Returns:
        Tuple[bool, str, bool]: Success status, message, and exists flag
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute(
            "SELECT COUNT(*) FROM excel_files WHERE client_id = ? AND file_name = ? AND index_name = ?",
            (client_id, file_name, index_name)
        )

        count = cursor.fetchone()[0]
        conn.close()

        exists = count > 0
        message = f"Excel file {'exists' if exists else 'does not exist'} for client {client_id}"

        return True, message, exists

    except Exception as e:
        return False, f"Error checking Excel file existence: {str(e)}", False

# Initialize the database when the module is imported
init_db()
