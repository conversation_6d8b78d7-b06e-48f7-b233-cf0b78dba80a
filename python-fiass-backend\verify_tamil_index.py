#!/usr/bin/env python3
"""
Verify Tamil FAISS index can be loaded correctly.
"""

import os
import sys
import json
import faiss

def verify_tamil_index():
    """Verify that the Tamil FAISS index can be loaded."""
    
    print("🔍 Verifying Tamil FAISS Index")
    print("=" * 40)
    
    # Path to Tamil index
    tamil_index_dir = "faiss_data/default-tamil"
    faiss_file = os.path.join(tamil_index_dir, "news_index.faiss")
    metadata_file = os.path.join(tamil_index_dir, "news_metadata.json")
    
    print(f"Tamil index directory: {tamil_index_dir}")
    print(f"FAISS file: {faiss_file}")
    print(f"Metadata file: {metadata_file}")
    
    # Check if files exist
    if not os.path.exists(faiss_file):
        print(f"❌ FAISS file not found: {faiss_file}")
        return False
        
    if not os.path.exists(metadata_file):
        print(f"❌ Metadata file not found: {metadata_file}")
        return False
    
    print("✅ Files exist")
    
    try:
        # Load FAISS index
        print("\n📂 Loading FAISS index...")
        faiss_index = faiss.read_index(faiss_file)
        print(f"✅ FAISS index loaded successfully")
        print(f"   - Total vectors: {faiss_index.ntotal}")
        print(f"   - Dimension: {faiss_index.d}")
        
        # Load metadata
        print("\n📄 Loading metadata...")
        with open(metadata_file, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        print(f"✅ Metadata loaded successfully")
        print(f"   - Total entries: {len(metadata)}")
        
        # Show sample metadata
        if metadata:
            print(f"\n📋 Sample metadata entry:")
            sample = metadata[0]
            for key, value in sample.items():
                if isinstance(value, str) and len(value) > 100:
                    value = value[:100] + "..."
                print(f"   - {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading index: {e}")
        return False

if __name__ == "__main__":
    success = verify_tamil_index()
    if success:
        print("\n🎉 Tamil index verification successful!")
    else:
        print("\n💥 Tamil index verification failed!")
        sys.exit(1)
